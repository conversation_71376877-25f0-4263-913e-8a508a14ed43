import { Builder, By, Key, until } from 'selenium-webdriver';
import { describe, it, before, after } from 'mocha';
import { strict as assert } from 'assert';

const URL = 'https://app.todoist.com/app';
const EMAIL_XPATH = '//input[@placeholder="Enter your email..."]';
const PASSWORD_XPATH = '//input[@placeholder="Enter your password..."]';
const LOGIN_BTN_XPATH = '//button[contains(text(),"Log in")]';

// Helper to clear input field
async function clearInput(element) {
  const value = await element.getAttribute('value');
  for (let i = 0; i < value.length; i++) {
    await element.sendKeys(Key.BACK_SPACE);
  }
}

describe('Todoist Login Page - Selenium', function() {
  this.timeout(30000);
  let driver;

  before(async () => {
    driver = await new Builder().forBrowser('chrome').build();
    await driver.get(URL);
  });

  after(async () => {
    await driver.quit();
  });

  it('should show error for wrong email and password', async () => {
    const email = await driver.wait(until.elementLocated(By.xpath(EMAIL_XPATH)));
    await clearInput(email);
    await email.sendKeys('<EMAIL>', Key.ENTER);
    const password = await driver.wait(until.elementLocated(By.xpath(PASSWORD_XPATH)));
    await clearInput(password);
    await password.sendKeys('WrongPass123', Key.ENTER);
    const loginBtn = await driver.wait(until.elementLocated(By.xpath(LOGIN_BTN_XPATH)));
    await loginBtn.click();
    const error = await driver.wait(until.elementLocated(By.xpath("//div[text()='Wrong email or password.']")));
    assert.equal(await error.getText(), 'Wrong email or password.');
  });

  it('should show error for correct email, wrong password', async () => {
    const email = await driver.wait(until.elementLocated(By.xpath(EMAIL_XPATH)));
    await clearInput(email);
    await email.sendKeys('<EMAIL>', Key.ENTER);
    const password = await driver.wait(until.elementLocated(By.xpath(PASSWORD_XPATH)));
    await clearInput(password);
    await password.sendKeys('WrongPass123', Key.ENTER);
    const loginBtn = await driver.wait(until.elementLocated(By.xpath(LOGIN_BTN_XPATH)));
    await loginBtn.click();
    const error = await driver.wait(until.elementLocated(By.xpath("//div[text()='Wrong email or password.']")));
    assert.equal(await error.getText(), 'Wrong email or password.');
  });

  it('should login with correct credentials', async () => {
    const email = await driver.wait(until.elementLocated(By.xpath(EMAIL_XPATH)));
    await clearInput(email);
    await email.sendKeys('<EMAIL>', Key.ENTER);
    const password = await driver.wait(until.elementLocated(By.xpath(PASSWORD_XPATH)));
    await clearInput(password);
    await password.sendKeys('Srihari1@', Key.ENTER);
    const loginBtn = await driver.wait(until.elementLocated(By.xpath(LOGIN_BTN_XPATH)));
    await loginBtn.click();
    await driver.sleep(2000);
    const title = await driver.getTitle();
    assert.equal(title, 'Todoist');
  });

  it('should show error for empty email field', async () => {
    const email = await driver.wait(until.elementLocated(By.xpath(EMAIL_XPATH)));
    await clearInput(email);
    await email.sendKeys(Key.ENTER);
    const password = await driver.wait(until.elementLocated(By.xpath(PASSWORD_XPATH)));
    await clearInput(password);
    await password.sendKeys('Srihari1@', Key.ENTER);
    const loginBtn = await driver.wait(until.elementLocated(By.xpath(LOGIN_BTN_XPATH)));
    await loginBtn.click();
    const error = await driver.wait(until.elementLocated(By.xpath("//div[text()='Please enter a valid email address']")));
    assert.equal(await error.getText(), 'Please enter a valid email address');
  });

  it('should show error for empty password field', async () => {
    const email = await driver.wait(until.elementLocated(By.xpath(EMAIL_XPATH)));
    await clearInput(email);
    await email.sendKeys('<EMAIL>', Key.ENTER);
    const password = await driver.wait(until.elementLocated(By.xpath(PASSWORD_XPATH)));
    await clearInput(password);
    await password.sendKeys(Key.ENTER);
    const loginBtn = await driver.wait(until.elementLocated(By.xpath(LOGIN_BTN_XPATH)));
    await loginBtn.click();
    const error = await driver.wait(until.elementLocated(By.xpath("//div[text()='Please enter your password']")));
    assert.equal(await error.getText(), 'Please enter your password');
  });

  it('should show error for both fields empty', async () => {
    const email = await driver.wait(until.elementLocated(By.xpath(EMAIL_XPATH)));
    await clearInput(email);
    await email.sendKeys(Key.ENTER);
    const password = await driver.wait(until.elementLocated(By.xpath(PASSWORD_XPATH)));
    await clearInput(password);
    await password.sendKeys(Key.ENTER);
    const loginBtn = await driver.wait(until.elementLocated(By.xpath(LOGIN_BTN_XPATH)));
    await loginBtn.click();
    const error = await driver.wait(until.elementLocated(By.xpath("//div[text()='Please enter a valid email address']")));
    assert.equal(await error.getText(), 'Please enter a valid email address');
  });

  it('should show error for invalid email format', async () => {
    const email = await driver.wait(until.elementLocated(By.xpath(EMAIL_XPATH)));
    await clearInput(email);
    await email.sendKeys('invalidemail', Key.ENTER);
    const password = await driver.wait(until.elementLocated(By.xpath(PASSWORD_XPATH)));
    await clearInput(password);
    await password.sendKeys('Srihari1@', Key.ENTER);
    const loginBtn = await driver.wait(until.elementLocated(By.xpath(LOGIN_BTN_XPATH)));
    await loginBtn.click();
    const error = await driver.wait(until.elementLocated(By.xpath("//div[text()='Please enter a valid email address']")));
    assert.equal(await error.getText(), 'Please enter a valid email address');
  });

  it('should show error for password too short', async () => {
    const email = await driver.wait(until.elementLocated(By.xpath(EMAIL_XPATH)));
    await clearInput(email);
    await email.sendKeys('<EMAIL>', Key.ENTER);
    const password = await driver.wait(until.elementLocated(By.xpath(PASSWORD_XPATH)));
    await clearInput(password);
    await password.sendKeys('a', Key.ENTER);
    const loginBtn = await driver.wait(until.elementLocated(By.xpath(LOGIN_BTN_XPATH)));
    await loginBtn.click();
    const error = await driver.wait(until.elementLocated(By.xpath("//div[contains(text(),'password')]")));
    assert.ok((await error.getText()).toLowerCase().includes('password'));
  });

  it('should show error for email with spaces', async () => {
    const email = await driver.wait(until.elementLocated(By.xpath(EMAIL_XPATH)));
    await clearInput(email);
    await email.sendKeys('  <EMAIL>  ', Key.ENTER);
    const password = await driver.wait(until.elementLocated(By.xpath(PASSWORD_XPATH)));
    await clearInput(password);
    await password.sendKeys('Srihari1@', Key.ENTER);
    const loginBtn = await driver.wait(until.elementLocated(By.xpath(LOGIN_BTN_XPATH)));
    await loginBtn.click();
    const error = await driver.wait(until.elementLocated(By.xpath("//div[text()='Wrong email or password.']")));
    assert.equal(await error.getText(), 'Wrong email or password.');
  });

  it('should show error for SQL injection attempt', async () => {
    const email = await driver.wait(until.elementLocated(By.xpath(EMAIL_XPATH)));
    await clearInput(email);
    await email.sendKeys("' OR '1'='1", Key.ENTER);
    const password = await driver.wait(until.elementLocated(By.xpath(PASSWORD_XPATH)));
    await clearInput(password);
    await password.sendKeys('Srihari1@', Key.ENTER);
    const loginBtn = await driver.wait(until.elementLocated(By.xpath(LOGIN_BTN_XPATH)));
    await loginBtn.click();
    const error = await driver.wait(until.elementLocated(By.xpath("//div[text()='Please enter a valid email address']")));
    assert.equal(await error.getText(), 'Please enter a valid email address');
  });

  it('should show error for XSS attempt', async () => {
    const email = await driver.wait(until.elementLocated(By.xpath(EMAIL_XPATH)));
    await clearInput(email);
    await email.sendKeys('<script>alert(1)</script>', Key.ENTER);
    const password = await driver.wait(until.elementLocated(By.xpath(PASSWORD_XPATH)));
    await clearInput(password);
    await password.sendKeys('Srihari1@', Key.ENTER);
    const loginBtn = await driver.wait(until.elementLocated(By.xpath(LOGIN_BTN_XPATH)));
    await loginBtn.click();
    const error = await driver.wait(until.elementLocated(By.xpath("//div[text()='Please enter a valid email address']")));
    assert.equal(await error.getText(), 'Please enter a valid email address');
  });
});
