import { Builder, By, Key, until } from "selenium-webdriver";

let driver = await new Builder().forBrowser("chrome").build();

await driver.get("https://app.todoist.com/app")

async function clearDataInFields(data){
    const emailValue = await data.getAttribute("value");
console.log("Data Value:", emailValue);
console.log("Length of entered email:", emailValue.length);

// Use loop to remove characters
for (let i = 0; i < emailValue.length; i++) {
  await email1.sendKeys(Key.BACK_SPACE);
}
}

// Wrong Email and Password
const email1 = await driver.wait(until.elementLocated(By.xpath('//input[@placeholder="Enter your email..."]')));
  await email1.sendKeys('<EMAIL>', Key.ENTER);

  const password1 = await driver.wait(until.elementLocated(By.xpath('//input[@placeholder="Enter your password..."]')));
  await driver.sleep(2000)
  await password1.sendKeys('Srihari1@', Key.ENTER);

  const confirmButton1 = await driver.wait(until.elementLocated(By.xpath('//button[contains(text(),"Log in")]')));
  await confirmButton1.click();

  const error1 = await driver.wait(until.elementLocated(By.xpath("//div[text()='Wrong email or password.']")));
  const message1 = await error1.getText();

  console.log(message1 === "Wrong email or password." ? "Test Case 1 Passed" : "Test Case 1 Failed");

  // --- Case 2: Wrong Password ---
  const email2 = await driver.wait(until.elementLocated(By.xpath('//input[@placeholder="Enter your email..."]')));
  await email2.sendKeys(Key.BACK_SPACE);
  await email2.sendKeys('<EMAIL>', Key.ENTER);

  const password2 = await driver.wait(until.elementLocated(By.xpath('//input[@placeholder="Enter your password..."]')));
  await password2.sendKeys(Key.BACK_SPACE);
  await password2.sendKeys('Srihari12@', Key.ENTER);

  const confirmButton2 = await driver.wait(until.elementLocated(By.xpath('//button[contains(text(),"Log in")]')));
  await confirmButton2.click();

  const error2 = await driver.wait(until.elementLocated(By.xpath("//div[text()='Wrong email or password.']")));
  const message2 = await error2.getText();

  console.log(message2 === "Wrong email or password." ? "Test Case 1 Passed" : "Test Case 1 Failed");

  // --- Case 3: Correct Credentials ---
  const email3 = await driver.wait(until.elementLocated(By.xpath('//input[@placeholder="Enter your email..."]')));
  await email3.sendKeys(Key.BACK_SPACE);
  await email3.sendKeys('<EMAIL>', Key.ENTER);

  const password3 = await driver.wait(until.elementLocated(By.xpath('//input[@placeholder="Enter your password..."]')));
  await password3.sendKeys(Key.BACK_SPACE);
  await password3.sendKeys('Srihari1@', Key.ENTER);

  const confirmButton3 = await driver.wait(until.elementLocated(By.xpath('//button[contains(text(),"Log in")]')));
  await confirmButton3.click(); 
  await driver.sleep(2000)

  const dashboardTitle = await driver.getTitle();
  console.log(dashboardTitle === 'Todoist' ? "Test Case 3 Passed" : "Test Case 3 Failed");
  await driver.sleep(2000)

//   // --- Case 4: Empty Email Field ---
//   const email4 = await driver.wait(until.elementLocated(By.xpath('//input[@placeholder="Enter your email..."]')));
//   await email4.clear();
//   await email4.sendKeys(Key.ENTER);

//   const password4 = await driver.wait(until.elementLocated(By.xpath('//input[@placeholder="Enter your password..."]')));
//   await password4.sendKeys('Srihari1@', Key.ENTER);

//   const confirmButton4 = await driver.wait(until.elementLocated(By.xpath('//button[contains(text(),"Log in")]')));
//   await confirmButton4.click();
//   await driver.sleep(2000)

//   const error4 = await driver.wait(until.elementLocated(By.xpath("//div[text()='Please enter a valid email address']")));
//   const message4 = await error4.getText();
//   console.log(message4 === "Please enter a valid email address" ? "Test Case 4 Passed" : "Test Case 4 Failed");
//   await driver.sleep(2000)



  await driver.quit();ß