// Licensed to the Software Freedom Conservancy (SFC) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The SFC licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

'use strict'

/**
 * @fileoverview Defines well-known symbols used within the selenium-webdriver
 * library.
 */

module.exports = {
  /**
   * The serialize symbol specifies a method that returns an object's serialized
   * representation. If an object's serialized form is not immediately
   * available, the serialize method will return a promise that will be resolved
   * with the serialized form.
   *
   * Note that the described method is analogous to objects that define a
   * `toJSON()` method, except the serialized result may be a promise, or
   * another object with a promised property.
   */
  serialize: Symbol('serialize'),
}
