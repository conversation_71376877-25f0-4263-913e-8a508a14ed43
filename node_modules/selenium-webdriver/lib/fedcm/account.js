// Licensed to the Software Freedom Conservancy (SFC) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The SFC licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

class Account {
  constructor(
    accountId,
    email,
    name,
    givenName,
    pictureUrl,
    idpConfigUrl,
    loginState,
    termsOfServiceUrl,
    privacyPolicyUrl,
  ) {
    this._accountId = accountId
    this._email = email
    this._name = name
    this._givenName = givenName
    this._pictureUrl = pictureUrl
    this._idpConfigUrl = idpConfigUrl
    this._loginState = loginState
    this._termsOfServiceUrl = termsOfServiceUrl
    this._privacyPolicyUrl = privacyPolicyUrl
  }

  get accountId() {
    return this._accountId
  }

  get email() {
    return this._email
  }

  get name() {
    return this._name
  }

  get givenName() {
    return this._givenName
  }

  get pictureUrl() {
    return this._pictureUrl
  }

  get idpConfigUrl() {
    return this._idpConfigUrl
  }

  get loginState() {
    return this._loginState
  }

  get termsOfServiceUrl() {
    return this._termsOfServiceUrl
  }

  get privacyPolicyUrl() {
    return this._privacyPolicyUrl
  }
}

module.exports = Account
