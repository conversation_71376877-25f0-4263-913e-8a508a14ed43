// Licensed to the Software Freedom Conservancy (SFC) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The SFC licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

/**
 * @fileoverview Demonstrates how to use WebDriver's logging sysem.
 */

'use strict'

const chrome = require('../chrome')
const firefox = require('../firefox')
const edge = require('../edge')
const { Builder, By, Key, logging, until } = require('..')

logging.installConsoleHandler()
logging.getLogger(`${logging.Type.DRIVER}.http`).setLevel(logging.Level.ALL)
;(async function () {
  let driver
  try {
    driver = await new Builder()
      // Default to using Firefox. This can be overridden at runtime by
      // setting the SELENIUM_BROWSER environment variable:
      //
      //   SELENIUM_BROWSER=chrome node selenium-webdriver/example/logging.js
      .forBrowser('firefox')
      // Configure the service for each browser to enable verbose logging and
      // to inherit the stdio settings from the current process. The builder
      // will only start the service if needed for the selected browser.
      .setChromeService(new chrome.ServiceBuilder().enableVerboseLogging().setStdio('inherit'))
      .setEdgeService(
        process.platform === 'win32' ? new edge.ServiceBuilder().enableVerboseLogging().setStdio('inherit') : null,
      )
      .setFirefoxService(new firefox.ServiceBuilder().enableVerboseLogging().setStdio('inherit'))
      .build()

    await driver.get('http://www.google.com/ncr')
    await driver.findElement(By.name('q')).sendKeys('webdriver', Key.RETURN)
    await driver.wait(until.titleIs('webdriver - Google Search'), 1000)
  } finally {
    if (driver) {
      await driver.quit()
    }
  }
})()
